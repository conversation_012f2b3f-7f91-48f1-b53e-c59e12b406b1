package com.stpl.tech.attendance.generator;

import com.gs.fw.common.mithra.generator.MithraGenerator;

/**
 * Simple wrapper to run Reladomo code generation
 */
public class ReladomoGenerator {
    
    public static void main(String[] args) {
        if (args.length < 3) {
            System.err.println("Usage: ReladomoGenerator <mithraClassList> <outputDir> <nonGeneratedDir> [generateGscListMethod]");
            System.exit(1);
        }
        
        String mithraClassList = args[0];
        String outputDir = args[1];
        String nonGeneratedDir = args[2];
        boolean generateGscListMethod = args.length > 3 ? Boolean.parseBoolean(args[3]) : true;
        
        try {
            System.out.println("Generating Reladomo classes...");
            System.out.println("MithraClassList: " + mithraClassList);
            System.out.println("Output Directory: " + outputDir);
            System.out.println("Non-Generated Directory: " + nonGeneratedDir);
            System.out.println("Generate GSC List Method: " + generateGscListMethod);
            
            MithraGenerator generator = new MithraGenerator();
            generator.setXml(mithraClassList);
            generator.setGeneratedDir(outputDir);
            generator.setNonGeneratedDir(nonGeneratedDir);
            generator.setGenerateGscListMethod(generateGscListMethod);
            generator.execute();
            
            System.out.println("Reladomo classes generated successfully!");
        } catch (Exception e) {
            System.err.println("Error generating Reladomo classes: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}
