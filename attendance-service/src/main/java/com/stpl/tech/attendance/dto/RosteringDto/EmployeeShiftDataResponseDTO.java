package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeShiftDataResponseDTO {
    private Integer employeeId;
    private List<EmployeeShiftDTO> shifts;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeShiftDTO {
        private Integer shiftId;
        private String shiftName;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private LocalDateTime date;
    }
}
