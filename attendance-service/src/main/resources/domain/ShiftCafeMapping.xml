<MithraObject>
    <PackageName>com.stpl.tech.attendance.domain</PackageName>
    <ClassName>ShiftCafeMapping</ClassName>
    <DefaultTable>SHIFT_CAFE_MAPPING</DefaultTable>
    
    <!-- Primary Key -->
    <Attribute name="id" javaType="Integer" primaryKey="true" identity="true"/>
    
    <!-- Business Attributes -->
    <Attribute name="shiftId" javaType="Integer" columnName="SHIFT_ID"/>
    <Attribute name="unitId" javaType="Integer" columnName="UNIT_ID"/>
    <Attribute name="status" javaType="String" columnName="STATUS"/>
    
    <!-- Audit Attributes -->
    <Attribute name="createdBy" javaType="String" columnName="CREATED_BY"/>
    <Attribute name="creationTime" javaType="Timestamp" columnName="CREATION_TIME"/>
    <Attribute name="updatedBy" javaType="String" columnName="UPDATED_BY"/>
    <Attribute name="updationTime" javaType="Timestamp" columnName="UPDATION_TIME"/>
    
    <!-- Bitemporal Attributes -->
    <Attribute name="businessFrom" javaType="Timestamp" columnName="BUSINESS_FROM"/>
    <Attribute name="businessTo" javaType="Timestamp" columnName="BUSINESS_TO"/>
    <Attribute name="processingFrom" javaType="Timestamp" columnName="PROCESSING_FROM"/>
    <Attribute name="processingTo" javaType="Timestamp" columnName="PROCESSING_TO"/>
    
    <!-- Bitemporal Configuration -->
    <Bitemporal>
        <BusinessDateAttribute>businessFrom</BusinessDateAttribute>
        <BusinessDateToAttribute>businessTo</BusinessDateToAttribute>
        <ProcessingDateAttribute>processingFrom</ProcessingDateAttribute>
        <ProcessingDateToAttribute>processingTo</ProcessingDateToAttribute>
    </Bitemporal>
    
    <!-- Indexes for Performance -->
    <Index name="IDX_SHIFT_CAFE_SHIFT_ID">
        <Attribute>shiftId</Attribute>
    </Index>
    <Index name="IDX_SHIFT_CAFE_UNIT_ID">
        <Attribute>unitId</Attribute>
    </Index>
    <Index name="IDX_SHIFT_CAFE_BUSINESS">
        <Attribute>businessFrom</Attribute>
        <Attribute>businessTo</Attribute>
    </Index>
    <Index name="IDX_SHIFT_CAFE_PROCESSING">
        <Attribute>processingFrom</Attribute>
        <Attribute>processingTo</Attribute>
    </Index>
</MithraObject> 