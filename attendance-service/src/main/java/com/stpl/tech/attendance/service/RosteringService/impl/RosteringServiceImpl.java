package com.stpl.tech.attendance.service.RosteringService.impl;

import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.dto.EmployeeMetadataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.CafeLiveDashboardDTO;
import com.stpl.tech.attendance.dto.RosteringDto.CafeLiveDashboardResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.CafeShiftDataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmployeeShiftDataResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.HierarchyEmployeeDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftCafeMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftEmployeesDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftEmployeesResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftResponseDTO;
import com.stpl.tech.attendance.entity.RosteringEntity.EmpShiftMapping;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCafeMapping;
import com.stpl.tech.attendance.repository.RosteringRepository.EmpShiftMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftCafeMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftRepository;
import com.stpl.tech.attendance.service.MetadataService;
import com.stpl.tech.attendance.service.RosteringService.RosteringService;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class HierarchyEmployeeListResponseDTO {
    private List<HierarchyEmployeeListResponseDTO.EmployeeDTO> employees;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeDTO {
        private String employeeId;
        private String name;
        private String email;
        private String role;
    }
}

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class RosteringServiceImpl implements RosteringService {

    @Autowired
    private MetadataService metadataService;

    private final ShiftRepository shiftRepository;

    private final EmpShiftMappingRepository empShiftMappingRepository;
    private final ShiftCafeMappingRepository shiftCafeMappingRepository;
    private final UserCacheService userCacheService;
    private final UnitCacheService unitCacheService;

    @Override
    @Transactional(readOnly = true)
    public CafeLiveDashboardResponseDTO getCafeLiveDashboard(Integer employeeId) {
        log.info("Getting cafe live dashboard data for employeeId: {}", employeeId);

        try {
            // Get employee details
            EmployeeBasicDetail employee = userCacheService.getUserById(employeeId);

            // Get employee's shift mappings
            List<EmpShiftMapping> empShiftMappings = empShiftMappingRepository
                .findByEmpIdAndStatus(employeeId, RosteringConstants.SHIFT_STATUS_ACTIVE);

            // Get shifts and build response
            List<CafeLiveDashboardResponseDTO.ShiftInfoDTO> shifts = new ArrayList<>();
            int totalActual = 0;

            for (EmpShiftMapping mapping : empShiftMappings) {
                Shift shift = shiftRepository.findById(mapping.getShiftId()).orElse(null);
                if (shift != null) {
                    // Get employee count for this shift
                    List<Integer> empIds = empShiftMappingRepository
                        .findEmpIdsByShiftIdAndStatus(shift.getShiftId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

                    CafeLiveDashboardResponseDTO.ShiftInfoDTO shiftInfo =
                        CafeLiveDashboardResponseDTO.ShiftInfoDTO.builder()
                            .shiftId(shift.getShiftId())
                            .startDate(shift.getStartTime())
                            .endDate(shift.getEndTime())
                            .numberOfEmployees(empIds.size())
                            .shiftName(shift.getShiftName())
                            .build();
                    shifts.add(shiftInfo);
                    totalActual += empIds.size();
                }
            }
            // Build dashboard view
            CafeLiveDashboardResponseDTO.DashboardViewDTO dashboardView =
                CafeLiveDashboardResponseDTO.DashboardViewDTO.builder()
                    .cafeDashboardView(CafeLiveDashboardResponseDTO.CafeDashboardViewDTO.builder()
                        .cafeDashboard(true)
                        .shiftDashboard(true)
                        .employeeDashboard(false)
                        .build())
                    .date(CafeLiveDashboardResponseDTO.DateRangeDTO.builder()
                            //todo
                        .startDate(shifts.get(0).getStartDate())
                        .endDate(shifts.get(shifts.size() - 1).getEndDate())
                        .build())
                    .cafeDashboard(CafeLiveDashboardResponseDTO.CafeDashboardStatsDTO.builder()
                        .actual(totalActual)
                        .ideal(totalActual + 30) // Example calculation
                        .build())
                    .shifts(shifts)
                    .build();

            return CafeLiveDashboardResponseDTO.builder()
                .dashboardView(dashboardView)
                .build();

        } catch (Exception e) {
            log.error("Error getting cafe live dashboard data", e);
            throw new RuntimeException("Failed to get cafe live dashboard data", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ShiftEmployeesResponseDTO getShiftEmployeesForUser(List<Integer> shiftIds, LocalDate date,Integer userId) {
        log.info("Getting shift employees for shiftIds: {}, date: {}, userId: {}", shiftIds, date, userId);
        
        // Get employees under the given userId
        List<EmployeeMetadataDTO> employees = metadataService.getEmployeeHierarchy(userId);
        
        // Create a set of employee IDs for efficient lookup
        Set<Integer> userEmployeeIds = employees.stream()
            .map(EmployeeMetadataDTO::getEmpId)
            .collect(Collectors.toSet());
        
        try {
            List<ShiftEmployeesResponseDTO.ShiftEmployeeDTO> shiftEmployees = new ArrayList<>();
            for (Integer shiftId : shiftIds) {
                // Fetch shift details
                Shift shift = shiftRepository.findById(shiftId).orElse(null);
                if (shift == null) continue;

                // Fetch employee shift mappings for this shift and date
                List<EmpShiftMapping> mappings = empShiftMappingRepository.findByShiftIdAndStatus(shiftId, RosteringConstants.SHIFT_STATUS_ACTIVE);

                for (EmpShiftMapping mapping : mappings) {
                    // Check if this employee is under the given userId
                    if (!userEmployeeIds.contains(mapping.getEmpId())) {
                        continue; // Skip employees not under the user's hierarchy
                    }
                    
                    // If date is provided, filter by mapping's startDate and endDate
                    boolean include = true;
                    if(date != null){
                        include = mapping.getExpectedStartDate() != null && mapping.getExpectedStartDate().toLocalDate().equals(date) ||
                                mapping.getExpectedEndDate() != null && mapping.getExpectedEndDate().toLocalDate().equals(date);
                    }
                    if (!include) continue;
                    
                    // Get employee details
                    EmployeeBasicDetail emp = userCacheService.getUserById(mapping.getEmpId());
                    if (emp != null) {
                        shiftEmployees.add(
                            ShiftEmployeesResponseDTO.ShiftEmployeeDTO.builder()
                                .shiftId(shift.getShiftId())
                                .employeeId(emp.getId())
                                .name(emp.getName())
                                .role(emp.getDesignation())
                                .build()
                        );
                    }
                }
            }

            return ShiftEmployeesResponseDTO.builder()
                .shifts(shiftEmployees)
                .build();

        } catch (Exception e) {
            log.error("Error getting shift employees", e);
            throw new RuntimeException("Failed to get shift employees", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public EmployeeShiftDataResponseDTO getEmpShiftData(Integer empId, LocalDateTime startDate, LocalDateTime endDate) {
        log.info("Getting employee shift data for empId: {}, startDate: {}, endDate: {}", empId, startDate, endDate);

        try {
            List<EmpShiftMapping> mappings = empShiftMappingRepository.findByEmpIdAndStatusAndExpectedDateRange(
                empId, RosteringConstants.SHIFT_STATUS_ACTIVE, startDate, endDate);

            List<EmployeeShiftDataResponseDTO.EmployeeShiftDTO> shifts = new ArrayList<>();

            for (EmpShiftMapping mapping : mappings) {
                Shift shift = shiftRepository.findById(mapping.getShiftId()).orElse(null);
                if (shift != null) {
                    EmployeeShiftDataResponseDTO.EmployeeShiftDTO shiftDto =
                        EmployeeShiftDataResponseDTO.EmployeeShiftDTO.builder()
                            .shiftId(shift.getShiftId())
                            .shiftName(shift.getShiftName())
                            .startTime(shift.getStartTime())
                            .endTime(shift.getEndTime())
                                //todo
//                            .date()
                            .build();

                    shifts.add(shiftDto);
                }
            }

            return EmployeeShiftDataResponseDTO.builder()
                .employeeId(empId)
                .shifts(shifts)
                .build();

        } catch (Exception e) {
            log.error("Error getting employee shift data for empId: {}", empId, e);
            throw new RuntimeException("Failed to get employee shift data", e);
        }
    }

    @Override
    public EmpShiftMappingDTO updateEmpShift(EmpShiftUpdateRequestDTO request) {
        log.warn("updateEmpShift method is deprecated. Use ReladomoEmpShiftService.updateEmpShifts() for bulk updates.");
        
        // For backward compatibility, handle single update if only one empId and shiftId
        if (request.getEmpIds() != null && request.getEmpIds().size() == 1 && 
            request.getShiftIds() != null && request.getShiftIds().size() == 1) {
            
            // Create a single mapping update
            EmpShiftMapping mapping = EmpShiftMapping.builder()
                .empId(request.getEmpIds().get(0))
                .shiftId(request.getShiftIds().get(0))
                .expectedStartDate(request.getExpectedArrivalTime()) // Use expectedArrivalTime as expectedStartDate
                .expectedEndDate(request.getBusinessTo()) // Use businessTo as expectedEndDate
                .processingFrom(LocalDateTime.now())
                .processingTo(LocalDateTime.of(9999, 12, 31, 23, 59, 59))
                .businessFrom(request.getBusinessFrom())
                .businessTo(request.getBusinessTo())
                .status("ACTIVE")
                .createdBy(request.getUpdatedBy())
                .updatedBy(request.getUpdatedBy())
                .build();

            mapping = empShiftMappingRepository.save(mapping);
            return convertToEmpShiftMappingDTO(mapping);
        } else {
            throw new UnsupportedOperationException("Bulk updates should use ReladomoEmpShiftService.updateEmpShifts()");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<CafeShiftDataDTO> getCafeShiftData(Integer employeeId) {
        log.info("Getting cafe shift data for employeeId: {}", employeeId);
        try {
            // 1. Get all active shift mappings for the employee
            List<EmpShiftMapping> empShiftMappings = empShiftMappingRepository.findByEmpIdAndStatus(employeeId, "ACTIVE");
            if (empShiftMappings == null || empShiftMappings.isEmpty()) {
                return new ArrayList<>();
            }

            // 2. Collect all unique shiftIds
            List<Integer> shiftIds = empShiftMappings.stream()
                    .map(EmpShiftMapping::getShiftId)
                    .distinct()
                    .toList();

            // 3. For each shiftId, get all mapped units (cafes) from ShiftCafeMapping
            //    Build a map: unitId -> set of shiftIds mapped for this employee
            Map<Integer, List<Integer>> unitToShiftIds = new java.util.HashMap<>();
            for (Integer shiftId : shiftIds) {
                List<ShiftCafeMapping> mappings = shiftCafeMappingRepository.findByShiftIdAndStatus(shiftId, "ACTIVE");
                for (ShiftCafeMapping mapping : mappings) {
                    unitToShiftIds.computeIfAbsent(mapping.getUnitId(), k -> new ArrayList<>()).add(shiftId);
                }
            }

            // 4. For each unit, build CafeShiftDataDTO
            List<CafeShiftDataDTO> result = new ArrayList<>();
            for (Map.Entry<Integer, List<Integer>> entry : unitToShiftIds.entrySet()) {
                Integer unitId = entry.getKey();
                List<Integer> mappedShiftIds = entry.getValue().stream().distinct().toList();
                UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
                if (unit == null) continue;

                // 5. For each shift mapped to this unit, get shift details
                List<CafeShiftDataDTO.ShiftDetailDTO> shiftDetails = new ArrayList<>();
                for (Integer shiftId : mappedShiftIds) {
                    Shift shift = shiftRepository.findById(shiftId).orElse(null);
                    if (shift == null) continue;
                    shiftDetails.add(
                        CafeShiftDataDTO.ShiftDetailDTO.builder()
                            .shiftId(shift.getShiftId())
                            .shiftName(shift.getShiftName())
                            .startTime(shift.getStartTime())
                            .endTime(shift.getEndTime())
                            .status(shift.getStatus())
                            .totalEmployees(empShiftMappingRepository.findEmpIdsByShiftIdAndStatus(shiftId, "ACTIVE").size())
                            .creationTime(shift.getCreationTime())
                            .createdBy(shift.getCreatedBy())
                            .build()
                    );
                }

                result.add(
                    CafeShiftDataDTO.builder()
                        .unitId(unit.getId())
                        .unitName(unit.getName())
                        .unitCode(unit.getReferenceName())
                        .shifts(shiftDetails)
                        .build()
                );
            }
            return result;
        } catch (Exception e) {
            log.error("Error getting cafe shift data", e);
            throw new RuntimeException("Failed to get cafe shift data", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<HierarchyEmployeeDTO> getHierarchyEmployees(Integer employeeId) {
        log.info("Getting direct subordinates for employeeId: {}", employeeId);
        try {
            Map<Integer, EmployeeBasicDetail> allEmployees = userCacheService.getAllUserCache();
            List<HierarchyEmployeeDTO> result = new ArrayList<>();
            for (EmployeeBasicDetail emp : allEmployees.values()) {
                if (employeeId != null && employeeId.equals(emp.getReportingManagerId())) {
                    result.add(HierarchyEmployeeDTO.builder()
                        .empId(emp.getId())
                        .empName(emp.getName())
                        .emailId(emp.getEmailId())
                        .designation(emp.getDesignation())
                        .build());
                }
            }
            return result;
        } catch (Exception e) {
            log.error("Error getting direct subordinates for employeeId: {}", employeeId, e);
            throw new RuntimeException("Failed to get hierarchy employees", e);
        }
    }

    @NotNull
    private static List<Map<String, Object>> getEmpList(Integer employeeId, Map<Integer, EmployeeBasicDetail> allEmployees) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (EmployeeBasicDetail emp : allEmployees.values()) {
            if (employeeId != null && employeeId.equals(emp.getReportingManagerId())) {
                Map<String, Object> empMap = new java.util.HashMap<>();
                empMap.put("employeeId", String.format("emp_%03d", emp.getId()));
                empMap.put("name", emp.getName());
                empMap.put("email", emp.getEmailId());
                empMap.put("role", emp.getDesignation());
                result.add(empMap);
            }
        }   
        return result;
    }

    // Additional utility methods implementation

    @Override
    public ShiftResponseDTO createShift(ShiftRequestDTO shiftRequestDTO, String createdBy) {
        log.info("Creating new shift: {}", shiftRequestDTO.getShiftName());
        if(shiftRequestDTO.getStartTime().isAfter(shiftRequestDTO.getEndTime())){
            throw new RuntimeException("Start time cannot be after end time");
        }
        // validate shift_name,startTime,endTime form db
        if(shiftRepository.existsByShiftNameIgnoreCaseAndStartTimeAndEndTimeAndStatus(shiftRequestDTO.getShiftName(),shiftRequestDTO.getStartTime(),shiftRequestDTO.getEndTime())){
            throw new RuntimeException("Shift with name startTime and endTime '" + shiftRequestDTO.getShiftName() + "' and startTime '" + shiftRequestDTO.getStartTime() + "' and endTime '" + shiftRequestDTO.getEndTime() + "' already exists");
        }
        try{
            Shift shift = Shift.builder()
                    .shiftName(shiftRequestDTO.getShiftName())
                    .startTime(shiftRequestDTO.getStartTime())
                    .endTime(shiftRequestDTO.getEndTime())
                    .status(RosteringConstants.SHIFT_STATUS_ACTIVE)
                    .createdBy(createdBy)
                    .creationTime(LocalDateTime.now())
                    .updatedBy(createdBy)
                    .updationTime(LocalDateTime.now())
                    .build();
             shiftRepository.save(shift);
            return convertToShiftResponseDTO(shift);
        } catch (Exception e) {
            log.error("Error creating shift", e);
            throw new RuntimeException("Failed to create shift", e);
        }
    }

    @Override
    public ShiftResponseDTO updateShift(Integer shiftId, ShiftRequestDTO shiftRequestDTO, String updatedBy) {
        log.info("Updating shift with ID: {}", shiftId);

        try {
            com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = shiftRepository.findById(shiftId)
                .orElseThrow(() -> new RuntimeException("Shift not found"));

            // Check if shift name already exists (excluding current shift)
            if (shiftRepository.existsByShiftNameIgnoreCaseAndShiftIdNotAndStatus(
                shiftRequestDTO.getShiftName(), shiftId)) {
                throw new RuntimeException("Shift with name '" + shiftRequestDTO.getShiftName() + "' already exists");
            }

            shift.setShiftName(shiftRequestDTO.getShiftName());
            shift.setStartTime(shiftRequestDTO.getStartTime());
            shift.setEndTime(shiftRequestDTO.getEndTime());
            shift.setUpdatedBy(updatedBy); // From HttpServletRequest

            shift = shiftRepository.save(shift);
            return convertToShiftResponseDTO(shift);

        } catch (Exception e) {
            log.error("Error updating shift", e);
            throw new RuntimeException("Failed to update shift", e);
        }
    }

    @Override
    public void deleteShift(Integer shiftId) {
        log.info("Deleting shift with ID: {}", shiftId);

        try {
            com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = shiftRepository.findById(shiftId)
                .orElseThrow(() -> new RuntimeException("Shift not found"));

            shift.setStatus("INACTIVE");
            shiftRepository.save(shift);

        } catch (Exception e) {
            log.error("Error deleting shift", e);
            throw new RuntimeException("Failed to delete shift", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ShiftResponseDTO> getAllShifts(String status) {
        log.info("Getting all shifts with status: {}", status);

        try {
            List<com.stpl.tech.attendance.entity.RosteringEntity.Shift> shifts = status != null ?
                shiftRepository.findByStatus(status) :
                shiftRepository.findAll();

            return shifts.stream()
                .map(this::convertToShiftResponseDTO)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error getting all shifts", e);
            throw new RuntimeException("Failed to get all shifts", e);
        }
    }

    @Override
    public ShiftCafeMappingDTO createShiftCafeMapping(Integer shiftId, Integer unitId, String createdBy) {
        log.info("Creating shift cafe mapping for shiftId: {}, unitId: {}", shiftId, unitId);

        try {
            // Check if mapping already exists
            if (shiftCafeMappingRepository.existsByShiftIdAndUnitIdAndStatus(shiftId, unitId, RosteringConstants.SHIFT_STATUS_ACTIVE)) {
                throw new RuntimeException("Shift cafe mapping already exists");
            }

            ShiftCafeMapping mapping = ShiftCafeMapping.builder()
                .shiftId(shiftId)
                .unitId(unitId)
                .status(RosteringConstants.SHIFT_STATUS_ACTIVE)
                .createdBy(createdBy)
                .updatedBy(createdBy)
                .build();

            mapping = shiftCafeMappingRepository.save(mapping);
            return convertToShiftCafeMappingDTO(mapping);

        } catch (Exception e) {
            log.error("Error creating shift cafe mapping", e);
            throw new RuntimeException("Failed to create shift cafe mapping", e);
        }
    }

    @Override
    public void deleteShiftCafeMapping(Integer shiftId, Integer unitId) {
        log.info("Deleting shift cafe mapping for shiftId: {}, unitId: {}", shiftId, unitId);

        try {
            ShiftCafeMapping mapping = shiftCafeMappingRepository
                .findByShiftIdAndUnitIdAndStatus(shiftId, unitId, RosteringConstants.SHIFT_STATUS_ACTIVE)
                .orElseThrow(() -> new RuntimeException("Shift cafe mapping not found"));

            mapping.setStatus(RosteringConstants.SHIFT_STATUS_INACTIVE);
            shiftCafeMappingRepository.save(mapping);

        } catch (Exception e) {
            log.error("Error deleting shift cafe mapping", e);
            throw new RuntimeException("Failed to delete shift cafe mapping", e);
        }
    }

    @Override
    public EmpShiftMappingDTO createEmpShiftMapping(EmpShiftUpdateRequestDTO request) {
        log.info("Creating employee shift mapping: {}", request);

        try {
            // For bulk requests, use the first empId and shiftId
            Integer empId = request.getEmpIds() != null && !request.getEmpIds().isEmpty() ? 
                request.getEmpIds().get(0) : null;
            Integer shiftId = request.getShiftIds() != null && !request.getShiftIds().isEmpty() ? 
                request.getShiftIds().get(0) : null;
            
            if (empId == null || shiftId == null) {
                throw new IllegalArgumentException("Employee ID and Shift ID are required");
            }

            // Check for overlapping mappings
            List<EmpShiftMapping> overlapping = empShiftMappingRepository.findOverlappingMappings(
                empId, RosteringConstants.SHIFT_STATUS_ACTIVE, -1, request.getExpectedArrivalTime(), request.getBusinessTo());

            if (!overlapping.isEmpty()) {
                throw new RuntimeException("Employee already has overlapping shift mapping");
            }

            EmpShiftMapping mapping = EmpShiftMapping.builder()
                .empId(empId)
                .shiftId(shiftId)
                .expectedStartDate(request.getExpectedArrivalTime())
                .expectedEndDate(request.getBusinessTo())
                .processingFrom(LocalDateTime.now())
                .processingTo(LocalDateTime.of(9999, 12, 31, 23, 59, 59))
                .businessFrom(request.getBusinessFrom())
                .businessTo(request.getBusinessTo())
                .status(RosteringConstants.SHIFT_STATUS_ACTIVE)
                .createdBy(request.getUpdatedBy())
                .updatedBy(request.getUpdatedBy())
                .build();

            mapping = empShiftMappingRepository.save(mapping);
            return convertToEmpShiftMappingDTO(mapping);

        } catch (Exception e) {
            log.error("Error creating employee shift mapping", e);
            throw new RuntimeException("Failed to create employee shift mapping", e);
        }
    }

    @Override
    public void deleteEmpShiftMapping(Integer mappingId) {
        log.info("Deleting employee shift mapping with ID: {}", mappingId);

        try {
            EmpShiftMapping mapping = empShiftMappingRepository.findById(mappingId)
                .orElseThrow(() -> new RuntimeException("Employee shift mapping not found"));

            mapping.setStatus(RosteringConstants.SHIFT_STATUS_INACTIVE);
            empShiftMappingRepository.save(mapping);

        } catch (Exception e) {
            log.error("Error deleting employee shift mapping", e);
            throw new RuntimeException("Failed to delete employee shift mapping", e);
        }
    }

    // Private helper methods

    private CafeLiveDashboardDTO buildCafeDashboard(UnitBasicDetail unit) {
        // Get shifts for this unit
        List<ShiftCafeMapping> shiftMappings = shiftCafeMappingRepository
            .findByUnitIdAndStatusWithShift(unit.getId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

        List<CafeLiveDashboardDTO.ShiftSummaryDTO> shiftSummaries = shiftMappings.stream()
            .map(mapping -> {
                com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = mapping.getShift();
                List<Integer> empIds = empShiftMappingRepository
                    .findEmpIdsByShiftIdAndStatus(shift.getShiftId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

                return CafeLiveDashboardDTO.ShiftSummaryDTO.builder()
                    .shiftId(shift.getShiftId())
                    .shiftName(shift.getShiftName())
                    .shiftStartTime(shift.getStartTime())
                    .shiftEndTime(shift.getEndTime())
                    .totalEmployeesInShift(empIds.size())
                    .presentEmployeesInShift(0) // TODO: Calculate from attendance data
                    .absentEmployeesInShift(empIds.size()) // TODO: Calculate from attendance data
                    .build();
            })
            .collect(Collectors.toList());

        int totalEmployees = shiftSummaries.stream()
            .mapToInt(CafeLiveDashboardDTO.ShiftSummaryDTO::getTotalEmployeesInShift)
            .sum();

        return CafeLiveDashboardDTO.builder()
            .unitId(unit.getId())
            .unitName(unit.getName())
            .unitCode(unit.getReferenceName())
            .totalEmployees(totalEmployees)
            .presentEmployees(0) // TODO: Calculate from attendance data
            .absentEmployees(totalEmployees) // TODO: Calculate from attendance data
            .onBreakEmployees(0) // TODO: Calculate from attendance data
            .shiftSummaries(shiftSummaries)
            .lastUpdated(LocalDateTime.now())
            .build();
    }

    private ShiftEmployeesDTO buildShiftEmployees(com.stpl.tech.attendance.entity.RosteringEntity.Shift shift) {
        List<Integer> empIds = empShiftMappingRepository
            .findEmpIdsByShiftIdAndStatus(shift.getShiftId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

        List<ShiftEmployeesDTO.EmployeeShiftDetailDTO> employees = empIds.stream()
            .map(empId -> {
                EmployeeBasicDetail emp = userCacheService.getUserById(empId);
                if (emp != null) {
                    return ShiftEmployeesDTO.EmployeeShiftDetailDTO.builder()
                        .empId(emp.getId())
                        .empName(emp.getName())
                        .empCode(emp.getEmployeeCode())
                        .designation(emp.getDesignation())
                        .contactNumber(emp.getContactNumber())
                        .status(emp.getStatus().name())
                        .attendanceStatus("UNKNOWN") // TODO: Calculate from attendance data
                        .build();
                }
                return null;
            })
            .filter(emp -> emp != null)
            .collect(Collectors.toList());

        return ShiftEmployeesDTO.builder()
            .shiftId(shift.getShiftId())
            .shiftName(shift.getShiftName())
            .shiftStartTime(shift.getStartTime())
            .shiftEndTime(shift.getEndTime())
            .totalEmployees(employees.size())
            .employees(employees)
            .build();
    }

    private CafeShiftDataDTO buildCafeShiftData(UnitBasicDetail unit) {
        List<ShiftCafeMapping> shiftMappings = shiftCafeMappingRepository
            .findByUnitIdAndStatusWithShift(unit.getId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

        List<CafeShiftDataDTO.ShiftDetailDTO> shifts = shiftMappings.stream()
            .map(mapping -> {
                com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = mapping.getShift();
                List<Integer> empIds = empShiftMappingRepository
                    .findEmpIdsByShiftIdAndStatus(shift.getShiftId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

                return CafeShiftDataDTO.ShiftDetailDTO.builder()
                    .shiftId(shift.getShiftId())
                    .shiftName(shift.getShiftName())
                    .startTime(shift.getStartTime())
                    .endTime(shift.getEndTime())
                    .status(shift.getStatus())
                    .totalEmployees(empIds.size())
                    .creationTime(shift.getCreationTime())
                    .createdBy(shift.getCreatedBy())
                    .build();
            })
            .collect(Collectors.toList());

        return CafeShiftDataDTO.builder()
            .unitId(unit.getId())
            .unitName(unit.getName())
            .unitCode(unit.getReferenceName())
            .shifts(shifts)
            .build();
    }

    private Shift convertToShiftDTO(com.stpl.tech.attendance.entity.RosteringEntity.Shift shift) {
        return Shift.builder()
            .shiftId(shift.getShiftId())
            .shiftName(shift.getShiftName())
            .startTime(shift.getStartTime())
            .endTime(shift.getEndTime())
            .status(shift.getStatus())
            .createdBy(shift.getCreatedBy())
            .creationTime(shift.getCreationTime())
            .updatedBy(shift.getUpdatedBy())
            .updationTime(shift.getUpdationTime())
            .build();
    }

    private ShiftResponseDTO convertToShiftResponseDTO(com.stpl.tech.attendance.entity.RosteringEntity.Shift shift) {
        return ShiftResponseDTO.builder()
            .shiftId(shift.getShiftId()) // Auto-generated from DB
            .shiftName(shift.getShiftName())
            .startTime(shift.getStartTime())
            .endTime(shift.getEndTime())
            .status(shift.getStatus())
//            .createdBy(shift.getCreatedBy()) // Set from HttpServletRequest
//            .creationTime(shift.getCreationTime()) // Set from current system time
//            .updatedBy(shift.getUpdatedBy()) // Set from HttpServletRequest
//            .updationTime(shift.getUpdationTime()) // Set from current system time
            .build();
    }

    private ShiftCafeMappingDTO convertToShiftCafeMappingDTO(ShiftCafeMapping mapping) {
        String shiftName = null;
        String unitName = null;

        if (mapping.getShift() != null) {
            shiftName = mapping.getShift().getShiftName();
        }

        UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(mapping.getUnitId());
        if (unit != null) {
            unitName = unit.getName();
        }

        return ShiftCafeMappingDTO.builder()
            .shiftCafeMappingId(mapping.getShiftCafeMappingId())
            .shiftId(mapping.getShiftId())
            .shiftName(shiftName)
            .unitId(mapping.getUnitId())
            .unitName(unitName)
            .status(mapping.getStatus())
            .createdBy(mapping.getCreatedBy())
            .creationTime(mapping.getCreationTime())
            .updatedBy(mapping.getUpdatedBy())
            .updationTime(mapping.getUpdationTime())
            .build();
    }

    private EmpShiftMappingDTO convertToEmpShiftMappingDTO(EmpShiftMapping mapping) {
        String shiftName = null;
        String empName = null;
        String empCode = null;

        if (mapping.getShift() != null) {
            shiftName = mapping.getShift().getShiftName();
        }

        EmployeeBasicDetail emp = userCacheService.getUserById(mapping.getEmpId());
        if (emp != null) {
            empName = emp.getName();
            empCode = emp.getEmployeeCode();
        }

        return EmpShiftMappingDTO.builder()
            .id(mapping.getId())
            .shiftId(mapping.getShiftId())
            .shiftName(shiftName)
            .empId(mapping.getEmpId())
            .empName(empName)
            .empCode(empCode)

            .expectedStartDate(mapping.getExpectedStartDate())
            .expectedEndDate(mapping.getExpectedEndDate())
            .processingFrom(mapping.getProcessingFrom())
            .processingTo(mapping.getProcessingTo())
            .businessFrom(mapping.getBusinessFrom())
            .businessTo(mapping.getBusinessTo())
            .status(mapping.getStatus())
            .createdBy(mapping.getCreatedBy())
            .creationTime(mapping.getCreationTime())
            .updatedBy(mapping.getUpdatedBy())
            .updationTime(mapping.getUpdationTime())
            .build();
    }

    private HierarchyEmployeeDTO convertToHierarchyEmployeeDTO(EmployeeBasicDetail emp, int level) {
        String reportingManagerName = null;
        if (emp.getReportingManagerId() != null) {
            EmployeeBasicDetail manager = userCacheService.getUserById(emp.getReportingManagerId());
            if (manager != null) {
                reportingManagerName = manager.getName();
            }
        }

        return HierarchyEmployeeDTO.builder()
            .empId(emp.getId())
            .empName(emp.getName())
            .empCode(emp.getEmployeeCode())
            .designation(emp.getDesignation())
            .contactNumber(emp.getContactNumber())
            .emailId(emp.getEmailId())
            .status(emp.getStatus().name())
            .reportingManagerId(emp.getReportingManagerId())
            .reportingManagerName(reportingManagerName)
            .departmentId(emp.getDepartmentId())
            .departmentName(emp.getDepartmentName())
            .level(level)
            .subordinates(new ArrayList<>())
            .build();
    }
}
