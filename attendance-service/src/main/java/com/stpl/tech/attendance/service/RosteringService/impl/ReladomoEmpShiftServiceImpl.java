package com.stpl.tech.attendance.service.RosteringService.impl;

import com.gs.fw.common.mithra.MithraManager;
import com.gs.fw.common.mithra.MithraTransaction;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateResponseDTO;
import com.stpl.tech.attendance.service.RosteringService.ReladomoEmpShiftService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

// TODO: Import these once Reladomo classes are generated
// import com.stpl.tech.attendance.domain.EmpShiftMapping;
// import com.stpl.tech.attendance.domain.EmpShiftMappingFinder;
// import com.stpl.tech.attendance.domain.EmpShiftMappingList;

// Temporary imports for JPA-based implementation
import com.stpl.tech.attendance.entity.RosteringEntity.EmpShiftMapping;
import com.stpl.tech.attendance.repository.RosteringRepository.EmpShiftMappingRepository;

/**
 * Reladomo-based service for employee shift operations with bitemporal support
 * This service uses generated Reladomo classes for high-performance bitemporal operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReladomoEmpShiftServiceImpl implements ReladomoEmpShiftService {

    private final EmpShiftMappingRepository empShiftMappingRepository;

    @Override
    public EmpShiftUpdateResponseDTO updateEmpShifts(EmpShiftUpdateRequestDTO request) {
        log.info("Updating employee shift mappings with Reladomo bitemporal: empIds={}, shiftIds={}, businessFrom={}, businessTo={}",
                request.getEmpIds(), request.getShiftIds(), request.getBusinessFrom(), request.getBusinessTo());

        // Validate request
        validateShiftUpdateRequest(request);

        try {
            // Perform bitemporal update operations
            UpdateResult result = performReladomoBulkUpdate(request, null);

            log.info("Successfully updated {} employee shift mappings using bitemporal operations", result.getTotalUpdated());

            return EmpShiftUpdateResponseDTO.builder()
                .success(true)
                .message("Shifts updated successfully using bitemporal operations")
                .updatedShifts(request.getShiftIds())
                .updatedEmployees(request.getEmpIds())
                .totalUpdatedMappings(result.getTotalUpdated())
                .build();

        } catch (Exception e) {
            log.error("Error updating employee shift mappings with bitemporal operations", e);
            throw new RuntimeException("Failed to update employee shift mappings", e);
        }
    }

    /**
     * Perform bulk update using JPA entities with bitemporal pattern
     */
    private UpdateResult performReladomoBulkUpdate(EmpShiftUpdateRequestDTO request, MithraTransaction tx) {
        log.debug("Performing bitemporal bulk update for {} employees and {} shifts",
                request.getEmpIds().size(), request.getShiftIds().size());

        int totalUpdated = 0;
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime businessFrom = request.getBusinessFrom();
        LocalDateTime businessTo = request.getBusinessTo();
        LocalDateTime processingFrom = currentTime;
        LocalDateTime processingTo = LocalDateTime.of(9999, 12, 31, 23, 59, 59); // Infinity

        // For each employee-shift combination
        for (Integer empId : request.getEmpIds()) {
            for (Integer shiftId : request.getShiftIds()) {
                // Validate no overlapping shifts exist
//                validateNoOverlappingShifts(empId, businessFrom, businessTo, null);

                // Terminate existing active mappings for this employee in the business date range
                terminateExistingMappings(empId, businessFrom, processingFrom);

                // Create new bitemporal mapping
                EmpShiftMapping newMapping = createBitemporalMapping(
                    empId, shiftId, request, businessFrom, businessTo,
                    processingFrom, processingTo, currentTime);

                empShiftMappingRepository.save(newMapping);
                totalUpdated++;

                log.debug("Created bitemporal mapping: empId={}, shiftId={}, businessFrom={}, businessTo={}",
                         empId, shiftId, businessFrom, businessTo);
            }
        }

        return new UpdateResult(totalUpdated);
    }

    @Override
    public List<EmpShiftMappingDTO> findCurrentShiftsByEmpId(Integer empId, LocalDateTime businessDate) {
        log.debug("Finding current shifts for employee: {} at business date: {}", empId, businessDate);

        LocalDateTime businessTimestamp = businessDate != null ? businessDate : LocalDateTime.now();
        LocalDateTime currentTime = LocalDateTime.now();

        // Use JPA for bitemporal queries
        List<EmpShiftMapping> mappings = empShiftMappingRepository.findByEmpIdAndStatusAndBusinessDateRange(
                empId, "ACTIVE", toTimestamp(businessTimestamp), toTimestamp(currentTime));

        return mappings.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    public List<EmpShiftMappingDTO> findShiftHistoryByEmpId(Integer empId) {
        log.debug("Finding shift history for employee: {}", empId);

        LocalDateTime currentTime = LocalDateTime.now();

        // Use JPA for processing time queries
        List<EmpShiftMapping> mappings = empShiftMappingRepository.findByEmpIdAndProcessingTimeRange(
                empId, toTimestamp(currentTime));

        // Sort by business from date to get chronological order
        mappings.sort((a, b) -> {
            if (a.getBusinessFrom() == null && b.getBusinessFrom() == null) return 0;
            if (a.getBusinessFrom() == null) return -1;
            if (b.getBusinessFrom() == null) return 1;
            return a.getBusinessFrom().compareTo(b.getBusinessFrom());
        });

        return mappings.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }



    /**
     * Validate the update request
     */
    private void validateShiftUpdateRequest(EmpShiftUpdateRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }

        if (request.getEmpIds() == null || request.getEmpIds().isEmpty()) {
            throw new IllegalArgumentException("Employee IDs are required");
        }

        if (request.getShiftIds() == null || request.getShiftIds().isEmpty()) {
            throw new IllegalArgumentException("Shift IDs are required");
        }

        if (request.getBusinessFrom() == null) {
            throw new IllegalArgumentException("Business from date is required");
        }

        if (request.getBusinessTo() == null) {
            throw new IllegalArgumentException("Business to date is required");
        }

        if (request.getBusinessFrom().isAfter(request.getBusinessTo())) {
            throw new IllegalArgumentException("Business from date cannot be after business to date");
        }

        // Validate that all empIds and shiftIds are positive
        if (request.getEmpIds().stream().anyMatch(id -> id == null || id <= 0)) {
            throw new IllegalArgumentException("All employee IDs must be positive integers");
        }

        if (request.getShiftIds().stream().anyMatch(id -> id == null || id <= 0)) {
            throw new IllegalArgumentException("All shift IDs must be positive integers");
        }

        log.debug("Update request validation passed for {} employees and {} shifts",
                 request.getEmpIds().size(), request.getShiftIds().size());
    }

    /**
     * Helper class to hold update results
     */
    private static class UpdateResult {
        private final int totalUpdated;

        public UpdateResult(int totalUpdated) {
            this.totalUpdated = totalUpdated;
        }

        public int getTotalUpdated() {
            return totalUpdated;
        }
    }

    /**
     * Convert JPA EmpShiftMapping to DTO
     */
    private EmpShiftMappingDTO convertToDTO(EmpShiftMapping mapping) {
        if (mapping == null) {
            return null;
        }

        return EmpShiftMappingDTO.builder()
            .id(mapping.getId())
            .shiftId(mapping.getShiftId())
            .empId(mapping.getEmpId())
            .expectedStartDate(mapping.getExpectedStartDate())
            .expectedEndDate(mapping.getExpectedEndDate())
            .processingFrom(mapping.getProcessingFrom())
            .processingTo(mapping.getProcessingTo())
            .businessFrom(mapping.getBusinessFrom())
            .businessTo(mapping.getBusinessTo())
            .status(mapping.getStatus())
            .createdBy(mapping.getCreatedBy())
            .creationTime(mapping.getCreationTime())
            .updatedBy(mapping.getUpdatedBy())
            .updationTime(mapping.getUpdationTime())
            .build();
    }

    private Timestamp toTimestamp(LocalDateTime localDateTime) {
        return localDateTime != null ? Timestamp.valueOf(localDateTime) : null;
    }

    /**
     * Validate the update request
     */
    private void validateUpdateRequest(EmpShiftUpdateRequestDTO request) {
        if (request.getEmpIds() == null || request.getEmpIds().isEmpty()) {
            throw new IllegalArgumentException("Employee IDs cannot be null or empty");
        }
        if (request.getShiftIds() == null || request.getShiftIds().isEmpty()) {
            throw new IllegalArgumentException("Shift IDs cannot be null or empty");
        }
        if (request.getBusinessFrom() == null) {
            throw new IllegalArgumentException("Business from date cannot be null");
        }
        if (request.getBusinessTo() == null) {
            throw new IllegalArgumentException("Business to date cannot be null");
        }
        if (request.getBusinessFrom().isAfter(request.getBusinessTo())) {
            throw new IllegalArgumentException("Business from date must be before business to date");
        }
    }

    /**
     * Validate no overlapping shifts exist for the employee
     */
    private void validateNoOverlappingShifts(Integer empId, LocalDateTime businessFrom,
                                           LocalDateTime businessTo, Integer excludeId) {
        List<EmpShiftMapping> overlapping = empShiftMappingRepository.findOverlappingMappings(
            empId, "ACTIVE", excludeId != null ? excludeId : -1, businessFrom, businessTo);

        if (!overlapping.isEmpty()) {
            throw new IllegalStateException(
                String.format("Employee %d already has overlapping shift assignments in the specified date range", empId));
        }
    }

    /**
     * Terminate existing active mappings for an employee
     */
    private void terminateExistingMappings(Integer empId, LocalDateTime businessFrom, LocalDateTime processingFrom) {
        List<EmpShiftMapping> existingMappings = empShiftMappingRepository.findByEmpIdAndStatus(empId, "ACTIVE");

        for (EmpShiftMapping existing : existingMappings) {
            // Check if this mapping overlaps with the new business date range
            if (existing.getBusinessTo() == null || existing.getBusinessTo().isAfter(businessFrom)) {
                // Terminate the existing mapping by setting processing end date
                existing.setProcessingTo(processingFrom);
                empShiftMappingRepository.save(existing);

                log.debug("Terminated existing mapping for empId: {}, mappingId: {}", empId, existing.getId());
            }
        }
    }

    /**
     * Create a new bitemporal mapping
     */
    private EmpShiftMapping createBitemporalMapping(Integer empId, Integer shiftId,
                                                   EmpShiftUpdateRequestDTO request,
                                                   LocalDateTime businessFrom, LocalDateTime businessTo,
                                                   LocalDateTime processingFrom, LocalDateTime processingTo,
                                                   LocalDateTime currentTime) {
        return EmpShiftMapping.builder()
            .empId(empId)
            .shiftId(shiftId)
            .expectedStartDate(request.getExpectedArrivalTime())
            .expectedEndDate(null) // Will be set based on shift timing
            .businessFrom(businessFrom)
            .businessTo(businessTo)
            .processingFrom(processingFrom)
            .processingTo(processingTo)
            .status("ACTIVE")
            .createdBy(request.getUpdatedBy())
            .updatedBy(request.getUpdatedBy())
            .creationTime(currentTime)
            .updationTime(currentTime)
            .build();
    }
}