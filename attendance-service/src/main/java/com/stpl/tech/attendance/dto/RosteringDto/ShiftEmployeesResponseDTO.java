package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftEmployeesResponseDTO {
    private List<ShiftEmployeeDTO> shifts;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShiftEmployeeDTO {
        private Integer shiftId;
        private Integer employeeId;
        private String name;
        private String role;
    }
}
