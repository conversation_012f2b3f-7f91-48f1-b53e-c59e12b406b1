package com.stpl.tech.attendance.repository.RosteringRepository;

import com.stpl.tech.attendance.entity.RosteringEntity.EmpShiftMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface EmpShiftMappingRepository extends JpaRepository<EmpShiftMapping, Integer> {
    
    /**
     * Find all mappings by employee ID
     */
    List<EmpShiftMapping> findByEmpIdAndStatus(Integer empId, String status);
    
    /**
     * Find all mappings by shift ID
     */
    List<EmpShiftMapping> findByShiftIdAndStatus(Integer shiftId, String status);
    
    /**
     * Find employee shift mappings within expected date range
     */
    @Query("SELECT esm FROM EmpShiftMapping esm WHERE esm.empId = :empId AND esm.status = :status " +
           "AND ((:startDate IS NULL OR esm.expectedEndDate IS NULL OR esm.expectedEndDate >= :startDate) " +
           "AND (:endDate IS NULL OR esm.expectedStartDate IS NULL OR esm.expectedStartDate <= :endDate))")
    List<EmpShiftMapping> findByEmpIdAndStatusAndExpectedDateRange(
        @Param("empId") Integer empId, 
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate
    );
    
    /**
     * Find current active shift for employee
     */
    @Query("SELECT esm FROM EmpShiftMapping esm WHERE esm.empId = :empId AND esm.status = :status " +
           "AND (esm.expectedStartDate IS NULL OR esm.expectedStartDate <= :currentDate) " +
           "AND (esm.expectedEndDate IS NULL OR esm.expectedEndDate >= :currentDate)")
    Optional<EmpShiftMapping> findCurrentShiftByEmpId(
        @Param("empId") Integer empId, 
        @Param("status") String status,
        @Param("currentDate") LocalDateTime currentDate
    );
    
    /**
     * Find all employees for a specific shift
     */
    @Query("SELECT esm.empId FROM EmpShiftMapping esm WHERE esm.shiftId = :shiftId AND esm.status = :status")
    List<Integer> findEmpIdsByShiftIdAndStatus(@Param("shiftId") Integer shiftId, @Param("status") String status);
    
    /**
     * Find employee shift mappings with shift details
     */
    @Query("SELECT esm FROM EmpShiftMapping esm JOIN FETCH esm.shift s WHERE esm.empId = :empId AND esm.status = :status")
    List<EmpShiftMapping> findByEmpIdAndStatusWithShift(@Param("empId") Integer empId, @Param("status") String status);
    
    /**
     * Find overlapping shift mappings for employee
     */
    @Query("SELECT esm FROM EmpShiftMapping esm WHERE esm.empId = :empId AND esm.status = :status " +
           "AND esm.id != :excludeId " +
           "AND ((:startDate IS NULL OR esm.expectedEndDate IS NULL OR esm.expectedEndDate >= :startDate) " +
           "AND (:endDate IS NULL OR esm.expectedStartDate IS NULL OR esm.expectedStartDate <= :endDate))")
    List<EmpShiftMapping> findOverlappingMappings(
        @Param("empId") Integer empId,
        @Param("status") String status,
        @Param("excludeId") Integer excludeId,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );
    
    /**
     * Check if employee has active shift mapping
     */
    boolean existsByEmpIdAndStatus(Integer empId, String status);
    
    /**
     * Find mappings by multiple employee IDs and shift IDs with status
     */
    @Query("SELECT esm FROM EmpShiftMapping esm WHERE esm.empId IN :empIds AND esm.shiftId IN :shiftIds AND esm.status = :status")
    List<EmpShiftMapping> findByEmpIdInAndShiftIdInAndStatus(
        @Param("empIds") List<Integer> empIds, 
        @Param("shiftIds") List<Integer> shiftIds, 
        @Param("status") String status
    );
    
    /**
     * Find current active shifts for employee at business date (bitemporal query)
     */
    @Query("SELECT esm FROM EmpShiftMapping esm WHERE esm.empId = :empId AND esm.status = :status " +
           "AND esm.businessFrom <= :businessDate AND esm.businessTo > :businessDate " +
           "AND esm.processingFrom <= :currentTime AND esm.processingTo > :currentTime")
    List<EmpShiftMapping> findByEmpIdAndStatusAndBusinessDateRange(
        @Param("empId") Integer empId, 
        @Param("status") String status,
        @Param("businessDate") Timestamp businessDate,
        @Param("currentTime") Timestamp currentTime
    );
    
    /**
     * Find all mappings for employee at current processing time (bitemporal query)
     */
    @Query("SELECT esm FROM EmpShiftMapping esm WHERE esm.empId = :empId " +
           "AND esm.processingFrom <= :currentTime AND esm.processingTo > :currentTime " +
           "ORDER BY esm.businessFrom ASC")
    List<EmpShiftMapping> findByEmpIdAndProcessingTimeRange(
        @Param("empId") Integer empId,
        @Param("currentTime") Timestamp currentTime
    );
}
