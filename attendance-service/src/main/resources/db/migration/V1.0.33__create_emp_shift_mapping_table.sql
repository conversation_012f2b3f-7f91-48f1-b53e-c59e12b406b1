-- Create EMP_SHIFT_MAPPING table
CREATE TABLE EMP_SHIFT_MAPPING (
    MAPPING_ID INT AUTO_INCREMENT PRIMARY KEY,
    SHIFT_ID INT NOT NULL,
    EMP_ID INT NOT NULL,
    START_DATE TIMESTAMP NULL,
    END_DATE TIMESTAMP NULL,
    EXPECTED_START_DATE TIMESTAMP NULL,
    EXPECTED_END_DATE TIMESTAMP NULL,
    PROCESSING_FROM TIMESTAMP NULL,
    PROCESSING_TO TIMESTAMP NULL,
    BUSINESS_FROM TIMESTAMP NULL,
    BUSINESS_TO TIMESTAMP NULL,
    STATUS VARCHAR(45) NOT NULL DEFAULT 'ACTIVE',
    CREATED_BY VARCHAR(100),
    CREATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UPDATED_BY VARCHAR(100),
    UPDATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    CONSTRAINT FK_EMP_SHIFT_MAPPING_SHIFT_ID FOREIGN KEY (SHIFT_ID) REFERENCES SHIFTS(SHIFT_ID)
);

-- Create indexes for better performance
CREATE INDEX idx_emp_shift_mapping_shift_id ON EMP_SHIFT_MAPPING(SHIFT_ID);
CREATE INDEX idx_emp_shift_mapping_emp_id ON EMP_SHIFT_MAPPING(EMP_ID);
CREATE INDEX idx_emp_shift_mapping_status ON EMP_SHIFT_MAPPING(STATUS);
CREATE INDEX idx_emp_shift_mapping_start_date ON EMP_SHIFT_MAPPING(START_DATE);
CREATE INDEX idx_emp_shift_mapping_end_date ON EMP_SHIFT_MAPPING(END_DATE);

-- Create composite indexes for common queries
CREATE INDEX idx_emp_shift_mapping_emp_status ON EMP_SHIFT_MAPPING(EMP_ID, STATUS);
CREATE INDEX idx_emp_shift_mapping_shift_status ON EMP_SHIFT_MAPPING(SHIFT_ID, STATUS);
CREATE INDEX idx_emp_shift_mapping_date_range ON EMP_SHIFT_MAPPING(EMP_ID, START_DATE, END_DATE, STATUS);

-- Add comments
COMMENT ON TABLE EMP_SHIFT_MAPPING IS 'Maps employees to shifts with date ranges and time specifications';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.ID IS 'Primary key for employee shift mapping';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.SHIFT_ID IS 'Foreign key reference to SHIFTS table';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.EMP_ID IS 'Employee ID';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.START_DATE IS 'Start date of the shift assignment';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.END_DATE IS 'End date of the shift assignment';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.EXPECTED_START_DATE IS 'Expected start date for the shift';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.EXPECTED_END_DATE IS 'Expected end date for the shift';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.PROCESSING_FROM IS 'Processing start time';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.PROCESSING_TO IS 'Processing end time';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.BUSINESS_FROM IS 'Business hours start time';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.BUSINESS_TO IS 'Business hours end time';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.STATUS IS 'Status of the mapping (ACTIVE, INACTIVE)';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.CREATED_BY IS 'User who created the mapping';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.CREATION_TIME IS 'Timestamp when mapping was created';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.UPDATED_BY IS 'User who last updated the mapping';
COMMENT ON COLUMN EMP_SHIFT_MAPPING.UPDATION_TIME IS 'Timestamp when mapping was last updated';
