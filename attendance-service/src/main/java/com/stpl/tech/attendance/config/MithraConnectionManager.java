package com.stpl.tech.attendance.config;

import com.gs.fw.common.mithra.connectionmanager.SourcelessConnectionManager;
import com.gs.fw.common.mithra.databasetype.DatabaseType;
import com.gs.fw.common.mithra.bulkloader.BulkLoader;
import com.gs.fw.common.mithra.bulkloader.BulkLoaderException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.TimeZone;
import com.gs.fw.common.mithra.databasetype.MsSqlDatabaseType;

@Component
public class MithraConnectionManager implements SourcelessConnectionManager {
    
    @Value("${spring.datasource.url}")
    private String url;
    
    @Value("${spring.datasource.username}")
    private String username;
    
    @Value("${spring.datasource.password}")
    private String password;
    
    @Override
    public Connection getConnection() {
        try {
            return DriverManager.getConnection(url, username, password);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get DB connection", e);
        }
    }

    @Override
    public DatabaseType getDatabaseType() {
        return MsSqlDatabaseType.getInstance();
    }

    @Override
    public TimeZone getDatabaseTimeZone() {
        return TimeZone.getDefault();
    }

    @Override
    public BulkLoader createBulkLoader() throws BulkLoaderException {
        throw new UnsupportedOperationException("Bulk loading not implemented");
    }

    @Override
    public String getDatabaseIdentifier() {
        return "default";
    }
} 