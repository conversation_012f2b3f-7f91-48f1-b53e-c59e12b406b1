<MithraObject>
    <PackageName>com.stpl.tech.attendance.domain</PackageName>
    <ClassName>EmpShiftMapping</ClassName>
    <DefaultTable>EMP_SHIFT_MAPPING</DefaultTable>

    <!-- Primary Key - Fixed to match database schema -->
    <Attribute name="id" javaType="Integer" primaryKey="true" identity="true" columnName="ID"/>

    <!-- Business Attributes -->
    <Attribute name="shiftId" javaType="Integer" columnName="SHIFT_ID"/>
    <Attribute name="empId" javaType="Integer" columnName="EMP_ID"/>
    <Attribute name="expectedStartDate" javaType="Timestamp" columnName="EXPECTED_START_DATE"/>
    <Attribute name="expectedEndDate" javaType="Timestamp" columnName="EXPECTED_END_DATE"/>
    <Attribute name="status" javaType="String" columnName="STATUS"/>

    <!-- Audit Attributes -->
    <Attribute name="createdBy" javaType="String" columnName="CREATED_BY"/>
    <Attribute name="creationTime" javaType="Timestamp" columnName="CREATION_TIME"/>
    <Attribute name="updatedBy" javaType="String" columnName="UPDATED_BY"/>
    <Attribute name="updationTime" javaType="Timestamp" columnName="UPDATION_TIME"/>

    <!-- Bitemporal Attributes -->
    <Attribute name="businessFrom" javaType="Timestamp" columnName="BUSINESS_FROM"/>
    <Attribute name="businessTo" javaType="Timestamp" columnName="BUSINESS_TO"/>
    <Attribute name="processingFrom" javaType="Timestamp" columnName="PROCESSING_FROM"/>
    <Attribute name="processingTo" javaType="Timestamp" columnName="PROCESSING_TO"/>

    <!-- Bitemporal Configuration -->
    <Bitemporal>
        <BusinessDateAttribute>businessFrom</BusinessDateAttribute>
        <BusinessDateToAttribute>businessTo</BusinessDateToAttribute>
        <ProcessingDateAttribute>processingFrom</ProcessingDateAttribute>
        <ProcessingDateToAttribute>processingTo</ProcessingDateToAttribute>
    </Bitemporal>

    <!-- Indexes for Performance -->
    <Index name="IDX_EMP_SHIFT_EMP_ID">
        <Attribute>empId</Attribute>
    </Index>
    <Index name="IDX_EMP_SHIFT_SHIFT_ID">
        <Attribute>shiftId</Attribute>
    </Index>
    <Index name="IDX_EMP_SHIFT_BUSINESS">
        <Attribute>businessFrom</Attribute>
        <Attribute>businessTo</Attribute>
    </Index>
    <Index name="IDX_EMP_SHIFT_PROCESSING">
        <Attribute>processingFrom</Attribute>
        <Attribute>processingTo</Attribute>
    </Index>
</MithraObject>