package com.stpl.tech.attendance.service.RosteringService;

import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateResponseDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service interface for Reladomo-based employee shift operations with bitemporal support
 */
public interface ReladomoEmpShiftService {

    /**
     * Update employee shift mappings based on empIds and shiftIds using Reladomo
     */
    EmpShiftUpdateResponseDTO updateEmpShifts(EmpShiftUpdateRequestDTO request);

    /**
     * Find current active shifts for an employee at a specific business date
     */
    List<EmpShiftMappingDTO> findCurrentShiftsByEmpId(Integer empId, LocalDateTime businessDate);

    /**
     * Find complete shift history for an employee
     */
    List<EmpShiftMappingDTO> findShiftHistoryByEmpId(Integer empId);
} 