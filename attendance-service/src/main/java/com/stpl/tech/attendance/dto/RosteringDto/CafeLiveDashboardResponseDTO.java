package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CafeLiveDashboardResponseDTO {
    private DashboardViewDTO dashboardView;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DashboardViewDTO {
        private CafeDashboardViewDTO cafeDashboardView;
        private DateRangeDTO date;
        private CafeDashboardStatsDTO cafeDashboard;
        private List<ShiftInfoDTO> shifts;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CafeDashboardViewDTO {
        private boolean cafeDashboard;
        private boolean shiftDashboard;
        private boolean employeeDashboard;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DateRangeDTO {
        private LocalDateTime startDate; // Format: "2024-01-01"
        private LocalDateTime endDate;   // Format: "2024-01-31"
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CafeDashboardStatsDTO {
        private Integer actual;
        private Integer ideal;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShiftInfoDTO {
        private Integer shiftId;
        private LocalDateTime startDate;
        private LocalDateTime endDate;
        private Integer numberOfEmployees;
        private String shiftName;
    }
}
