package com.stpl.tech.attendance.service.RosteringService;

import com.stpl.tech.attendance.dto.RosteringDto.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public interface RosteringService {

    /**
     * Get cafe live dashboard data based on employee ID
     *
     * @param employeeId Employee ID to fetch dashboard data for
     * @return Cafe live dashboard response
     */
    CafeLiveDashboardResponseDTO getCafeLiveDashboard(Integer employeeId);

    /**
     * Get shift employees data
     * @param shiftId Optional shift ID filter
     * @param unitId Optional unit ID filter
     * @param pageable Pagination information
     * @return Shift employees response
     */
    ShiftEmployeesResponseDTO getShiftEmployeesForUser(List<Integer> shiftIds, LocalDate date,Integer userId);

    /**
     * Get employee shift data
     * @param empId Employee ID
     * @param startDate Optional start date filter
     * @param endDate Optional end date filter
     * @return Employee shift data response
     */
    EmployeeShiftDataResponseDTO getEmpShiftData(Integer empId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Update employee shift mapping
     * @param request Update request
     * @return Updated employee shift mapping
     */
    EmpShiftMappingDTO updateEmpShift(EmpShiftUpdateRequestDTO request);

    /**
     * Get cafe shift data
     * @param unitId Optional unit ID filter
     * @return List of cafe shift data
     */
    List<CafeShiftDataDTO> getCafeShiftData(Integer unitId);

    /**
     * Get hierarchy employees
     * @param managerId Optional manager ID to get subordinates
     * @param includeSubordinates Whether to include all subordinates recursively
     * @return List of hierarchy employees
     */
    List<HierarchyEmployeeDTO> getHierarchyEmployees(Integer employeeId);

    // Additional utility methods

    /**
     * Create a new shift
     * @param shiftRequestDTO Shift request data
     * @param createdBy Employee ID from HttpServletRequest
     * @return Created shift
     */
    ShiftResponseDTO createShift(ShiftRequestDTO shiftRequestDTO, String createdBy);

    /**
     * Update an existing shift
     * @param shiftId Shift ID
     * @param shiftRequestDTO Updated shift data
     * @param updatedBy Employee ID from HttpServletRequest
     * @return Updated shift
     */
    ShiftResponseDTO updateShift(Integer shiftId, ShiftRequestDTO shiftRequestDTO, String updatedBy);

    /**
     * Delete a shift
     * @param shiftId Shift ID
     */
    void deleteShift(Integer shiftId);

    /**
     * Get all shifts
     * @param status Optional status filter
     * @return List of shifts
     */
    List<ShiftResponseDTO> getAllShifts(String status);

    /**
     * Create shift cafe mapping
     * @param shiftId Shift ID
     * @param unitId Unit ID
     * @param createdBy Created by user
     * @return Created mapping
     */
    ShiftCafeMappingDTO createShiftCafeMapping(Integer shiftId, Integer unitId, String createdBy);

    /**
     * Delete shift cafe mapping
     * @param shiftId Shift ID
     * @param unitId Unit ID
     */
    void deleteShiftCafeMapping(Integer shiftId, Integer unitId);

    /**
     * Create employee shift mapping
     * @param request Employee shift mapping request
     * @return Created mapping
     */
    EmpShiftMappingDTO createEmpShiftMapping(EmpShiftUpdateRequestDTO request);

    /**
     * Delete employee shift mapping
     * @param mappingId Mapping ID
     */
    void deleteEmpShiftMapping(Integer mappingId);
}
